"""
Test script pre Football-Data.org API scraper.

Tento script testuje základnú funkcionalitu scrapera bez potreby
skutočného API kľúča (používa mock dáta).
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraping.football_data_scraper import FootballDataScraper


def test_odds_generation():
    """Test generovania placeholder kurzov."""
    print("Testovanie generovania kurzov...")
    
    scraper = FootballDataScraper("test_key")
    
    # Test rôznych výsledkov
    test_cases = [
        ("Manchester City", "Liverpool", 3, 1),  # <PERSON><PERSON><PERSON>
        ("Arsenal", "Chelsea", 1, 2),            # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        ("Tottenham", "Manchester United", 2, 2), # <PERSON><PERSON><PERSON><PERSON>
        ("Brighton", "Newcastle", 0, 1),         # Tesn<PERSON> výsledok
    ]
    
    for home, away, home_goals, away_goals in test_cases:
        odds = scraper._generate_realistic_odds(home, away, home_goals, away_goals)
        print(f"{home} {home_goals}-{away_goals} {away}")
        print(f"  Kurzy: D:{odds['home_odds']:.2f}, R:{odds['draw_odds']:.2f}, H:{odds['away_odds']:.2f}")
        
        # Základné validácie
        assert odds['home_odds'] >= 1.0, "Dom<PERSON>ci kurz musí byť >= 1.0"
        assert odds['draw_odds'] >= 1.0, "Kurz na remízu musí byť >= 1.0"
        assert odds['away_odds'] >= 1.0, "Hosťujúci kurz musí byť >= 1.0"
        
        # Kontrola rozumnosti kurzov
        total_prob = (1/odds['home_odds'] + 1/odds['draw_odds'] + 1/odds['away_odds'])
        assert 0.9 <= total_prob <= 1.2, f"Celková pravdepodobnosť {total_prob:.3f} nie je rozumná"
    
    print("✓ Test generovania kurzov úspešný!")


def test_data_transformation():
    """Test transformácie mock dát."""
    print("\nTestovanie transformácie dát...")
    
    scraper = FootballDataScraper("test_key")
    
    # Mock dáta v štýle Football-Data.org API
    mock_matches = [
        {
            "utcDate": "2023-08-12T14:00:00Z",
            "homeTeam": {"name": "Manchester City"},
            "awayTeam": {"name": "Burnley"},
            "score": {
                "fullTime": {
                    "homeTeam": 3,
                    "awayTeam": 0
                }
            }
        },
        {
            "utcDate": "2023-08-12T16:30:00Z",
            "homeTeam": {"name": "Arsenal"},
            "awayTeam": {"name": "Nottingham Forest"},
            "score": {
                "fullTime": {
                    "homeTeam": 2,
                    "awayTeam": 1
                }
            }
        },
        {
            "utcDate": "2023-08-13T14:00:00Z",
            "homeTeam": {"name": "Brighton"},
            "awayTeam": {"name": "Luton Town"},
            "score": {
                "fullTime": {
                    "homeTeam": None,  # Neukončený zápas
                    "awayTeam": None
                }
            }
        }
    ]
    
    csv_data = scraper.transform_matches_to_csv_format(mock_matches)
    
    # Kontroly
    assert len(csv_data) == 2, f"Očakávané 2 platné zápasy, získané {len(csv_data)}"
    
    for row in csv_data:
        required_fields = ["date", "home_team", "away_team", "home_goals", "away_goals", 
                          "home_odds", "draw_odds", "away_odds"]
        for field in required_fields:
            assert field in row, f"Chýba pole {field}"
        
        # Kontrola formátu dátumu
        assert len(row["date"]) == 10, "Dátum musí byť vo formáte YYYY-MM-DD"
        assert row["date"].count("-") == 2, "Dátum musí obsahovať 2 pomlčky"
        
        # Kontrola číselných hodnôt
        assert isinstance(row["home_goals"], int), "Góly musia byť celé čísla"
        assert isinstance(row["away_goals"], int), "Góly musia byť celé čísla"
        assert isinstance(row["home_odds"], float), "Kurzy musia byť desatinné čísla"
    
    print("✓ Test transformácie dát úspešný!")
    
    # Výpis výsledkov
    print("\nTransformované dáta:")
    for row in csv_data:
        print(f"  {row['date']}: {row['home_team']} {row['home_goals']}-{row['away_goals']} {row['away_team']}")


def test_csv_format_compatibility():
    """Test kompatibility s existujúcim formátom."""
    print("\nTestovanie kompatibility CSV formátu...")
    
    # Načítanie existujúceho súboru ak existuje
    if os.path.exists("data/raw_results.csv"):
        import pandas as pd
        existing_df = pd.read_csv("data/raw_results.csv")
        expected_columns = list(existing_df.columns)
        print(f"Existujúce stĺpce: {expected_columns}")
    else:
        expected_columns = ["date", "home_team", "away_team", "home_goals", "away_goals", 
                           "home_odds", "draw_odds", "away_odds"]
        print(f"Očakávané stĺpce: {expected_columns}")
    
    # Test s mock dátami
    scraper = FootballDataScraper("test_key")
    mock_matches = [{
        "utcDate": "2023-08-12T14:00:00Z",
        "homeTeam": {"name": "Test Home"},
        "awayTeam": {"name": "Test Away"},
        "score": {"fullTime": {"homeTeam": 1, "awayTeam": 0}}
    }]
    
    csv_data = scraper.transform_matches_to_csv_format(mock_matches)
    
    if csv_data:
        actual_columns = list(csv_data[0].keys())
        print(f"Generované stĺpce: {actual_columns}")
        
        # Kontrola kompatibility
        for col in expected_columns:
            assert col in actual_columns, f"Chýba stĺpec {col}"
        
        print("✓ Test kompatibility CSV formátu úspešný!")
    else:
        print("✗ Nepodarilo sa vygenerovať CSV dáta")


def main():
    """Spustenie všetkých testov."""
    print("Football-Data.org Scraper Tests")
    print("=" * 40)
    
    try:
        test_odds_generation()
        test_data_transformation()
        test_csv_format_compatibility()
        
        print("\n" + "=" * 40)
        print("✓ Všetky testy úspešné!")
        print("\nPre skutočné použitie:")
        print("1. Nastavte API_KEY_FOOTBALL_DATA v config.py")
        print("2. Spustite: python main.py --historical")
        
    except Exception as e:
        print(f"\n✗ Test zlyhal: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
