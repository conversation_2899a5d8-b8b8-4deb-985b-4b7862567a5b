# Football-Data.org API Integrácia - Návod na nastavenie

## Prehľad

Váš betting agent teraz podporuje sťahovanie historických futbalových dát z Football-Data.org API. Toto vám umožní:

- Získať tisíce historických zápasov z európskych líg
- Automaticky natrénovať modely na reálnych dátach
- Zlepšiť presnosť predikcie value betov

## Krok 1: Registrácia na Football-Data.org

1. **Navštívte**: https://www.football-data.org/
2. **Kliknite na**: "Get started" alebo "Sign up"
3. **Vyplňte registračný formulár**:
   - Email adresa
   - Heslo
   - Súhlas s podmienkami
4. **Potvrďte email** (skontrolujte spam folder)
5. **Prihláste sa** do vášho účtu

## Krok 2: Získanie API kľúča

1. **Po prihlásení** idite do vášho dashboard
2. **Nájdite sekciu** "API Key" alebo "Authentication"
3. **Skopírujte váš API kľúč** (vyzerá ako: `abc123def456...`)

## Krok 3: Konfigurácia v projekte

1. **Otvorte súbor** `config.py` v root adresári projektu
2. **Nájdite riadok**:
   ```python
   API_KEY_FOOTBALL_DATA = "YOUR_FOOTBALL_DATA_API_KEY"
   ```
3. **Nahraďte** `YOUR_FOOTBALL_DATA_API_KEY` vaším skutočným API kľúčom:
   ```python
   API_KEY_FOOTBALL_DATA = "abc123def456ghi789jkl012"
   ```

## Krok 4: Spustenie sťahovania historických dát

### Metóda 1: Cez main.py (odporúčané)
```bash
python main.py --historical
```

Toto automaticky:
- Stiahne historické dáta z vybraných líg
- Spustí predspracovanie dát
- Natrénuje modely na nových dátach

### Metóda 2: Priamo scraper
```bash
python scraping/football_data_scraper.py
```

## Čo sa stiahne

### Súťaže (predvolene):
- **PL**: Premier League (Anglicko)
- **BL1**: Bundesliga (Nemecko)  
- **SA**: Serie A (Taliansko)
- **PD**: Primera Division/La Liga (Španielsko)
- **FL1**: Ligue 1 (Francúzsko)
- **DED**: Eredivisie (Holandsko)
- **PPL**: Primeira Liga (Portugalsko)

### Sezóny (predvolene):
- 2020/21
- 2021/22
- 2022/23
- 2023/24

### Očakávaný objem dát:
- **Približne 2,500-3,000 zápasov** celkovo
- **Čas sťahovania**: 15-20 minút (kvôli rate limiting)

## Konfigurácia (voliteľné)

Môžete upraviť `config.py` pre vlastné potreby:

### Zmena súťaží:
```python
FOOTBALL_DATA_COMPETITIONS = {
    "PL": "Premier League",     # Len Premier League
    "BL1": "Bundesliga"         # A Bundesliga
}
```

### Zmena sezón:
```python
FOOTBALL_DATA_SEASONS = [2022, 2023]  # Len posledné 2 sezóny
```

## Rate Limiting

Football-Data.org má limity:
- **Free tier**: 10 requestov za minútu
- **Scraper automaticky** dodržiava tieto limity
- **Celkový čas**: závisí od počtu súťaží a sezón

## Riešenie problémov

### Chyba: "API key not found"
- Skontrolujte, či ste správne nastavili `API_KEY_FOOTBALL_DATA`
- Uistite sa, že API kľúč neobsahuje medzery na začiatku/konci

### Chyba: "Rate limit exceeded"
- Scraper automaticky čaká, len buďte trpezliví
- Free tier má limit 10 requestov/minútu

### Chyba: "No data retrieved"
- Skontrolujte internetové pripojenie
- Overte, že API kľúč je platný
- Skúste menší počet súťaží/sezón

### Chyba: "Permission denied"
- Niektoré súťaže môžu vyžadovať platený plán
- Skúste len základné súťaže (PL, BL1, SA)

## Výstup

Po úspešnom stiahnutí nájdete:
- **`data/raw_results.csv`**: Historické dáta zápasov
- **`data/processed_features.csv`**: Spracované features
- **`data/trained_models/`**: Natrénované modely

## Ďalšie kroky

Po stiahnutí historických dát môžete:

1. **Spustiť analýzu value betov**:
   ```bash
   python main.py
   ```

2. **Spustiť dashboard**:
   ```bash
   python dashboard/app.py
   ```

3. **Testovať modely**:
   ```bash
   python models/trainer.py
   ```

## Poznámky o kurzoch

Football-Data.org API **neposkytuje historické kurzy**, len výsledky zápasov. Scraper preto:
- Generuje **realistické placeholder kurzy**
- Založené na výsledku zápasu a pravdepodobnostiach
- Vhodné pre tréning modelov

Pre skutočné kurzy môžete kombinovať s The-Odds-API pre aktuálne zápasy.

## Podpora

Ak máte problémy:
1. Skontrolujte tento návod
2. Spustite test: `python test_football_data.py`
3. Skontrolujte log výstupy pre chybové hlásenia
