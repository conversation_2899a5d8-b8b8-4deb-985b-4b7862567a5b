import pandas as pd
import os
import sys
import xgboost as xgb
from sklearn.model_selection import ParameterGrid
from sklearn.metrics import log_loss
import joblib # Pre uloženie najlepš<PERSON>ch parametrov

from models.xgboost_model import XGBoostModel
from models.poisson_model import PoissonModel

# Pridanie cesty k 'data' modulu, aby sme mohli importovať data_preprocessing
# Toto je potrebné, pretože spúšťame skript z iného adresára
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from data.data_preprocessing import preprocess_data, PROCESSED_DATA_PATH, RAW_DATA_PATH

# Konštanty pre cesty k súborom
MODEL_DIR = 'data/trained_models'
XGB_MODEL_PATH = os.path.join(MODEL_DIR, 'xgboost_model.joblib')
POISSON_MODEL_PATH = os.path.join(MODEL_DIR, 'poisson_model.joblib')
XGB_BEST_PARAMS_PATH = os.path.join(MODEL_DIR, 'xgboost_best_params.joblib')


def train_and_save_models():
    """
    Hlavná funkcia pre trénovanie a uloženie modelov.
    """
    print("Spúšťa sa trénovanie modelov...")
    
    # 1. Krok: Predspracovanie dát pomocou nového skriptu
    print("Spúšťam predspracovanie dát...")
    try:
        df_features = preprocess_data()
        if df_features is None or df_features.empty:
            print("Chyba: Predspracovanie dát nevrátilo žiadne dáta alebo dáta sú prázdne. Skript sa ukončil.")
            return
        print(f"Dáta boli úspešne spracované a uložené do {PROCESSED_DATA_PATH}")
    except FileNotFoundError as e:
        print(f"Chyba: {e}. Skript sa ukončil.")
        return

    # Načítanie spracovaných dát (už je v df_features, ale pre konzistenciu s pôvodným kódom)
    # df_features = pd.read_csv(PROCESSED_DATA_PATH) # Toto už nie je potrebné, ak preprocess_data vracia DataFrame

    # Definovanie príznakov a cieľovej premennej (presunuté sem, aby boli dostupné pre obe vetvy)
    features = [
        'home_avg_scored', 'home_avg_conceded',
        'away_avg_scored', 'away_avg_conceded',
        'form_home', 'form_away',
        'elo_home', 'elo_away'
    ]
    target = 'result'
    
    X = df_features[features]
    y = df_features[target]

    # Kontrola na NaN hodnoty v X a y
    if X.isnull().values.any() or y.isnull().values.any():
        print("Chyba: Dáta pre XGBoost model obsahujú NaN hodnoty po predspracovaní. Skript sa ukončí.")
        return

    # Kontrola dostatočného množstva dát pre krížovú validáciu
    min_samples_for_cv = 2 # Minimálne 2 vzorky pre CV
    if X.shape[0] < min_samples_for_cv:
        print(f"Upozornenie: Nedostatok dát ({X.shape[0]} vzoriek) pre krížovú validáciu XGBoost modelu. Trénujem bez CV.")
        # Trénovanie finálneho XGBoost modelu bez CV
        xgb_model = XGBoostModel()
        xgb_model.train(X, y, num_boost_round=100, early_stopping_rounds=None) # Použijeme defaultné kolá
        xgb_model.save(XGB_MODEL_PATH)
        joblib.dump({}, XGB_BEST_PARAMS_PATH) # Uložíme prázdny dict pre parametre
        print("XGBoost model bol natrénovaný bez ladenia hyperparametrov kvôli nedostatku dát.")
    else:
        # Definícia priestoru hyperparametrov pre Grid Search
        param_grid = {
            'max_depth': [3, 5, 7],
            'eta': [0.01, 0.05, 0.1],
            'subsample': [0.7, 0.8, 0.9],
            'colsample_bytree': [0.7, 0.8, 0.9],
            'n_estimators': [100, 200, 300] # Toto bude použité ako num_boost_round
        }

        best_xgb_model = None
        best_logloss = float('inf')
        best_params = {}

        print("\nSpúšťam ladenie hyperparametrov pre XGBoost model...")
        dtrain = xgb.DMatrix(X, label=y)

        # Dynamicky nastaviť nfold
        nfold_cv = min(5, X.shape[0])
        if nfold_cv < 2:
            print(f"Upozornenie: Počet foldov pre CV je menší ako 2 ({nfold_cv}). Preskakujem CV.")
            # Ak je nfold_cv príliš malé, preskočíme CV a trénujeme s defaultnými parametrami
            best_params = {
                'objective': 'multi:softprob',
                'num_class': 3,
                'eval_metric': 'mlogloss',
                'eta': 0.1,
                'max_depth': 3,
                'seed': 42,
                'n_estimators': 100 # Default
            }
            print("XGBoost model bude trénovaný s defaultnými parametrami kvôli nedostatku dát pre CV.")
        else:
            for params in ParameterGrid(param_grid):
                current_params = {
                    'objective': 'multi:softprob',
                    'num_class': 3,
                    'eval_metric': 'mlogloss',
                    'eta': params['eta'],
                    'max_depth': params['max_depth'],
                    'subsample': params['subsample'],
                    'colsample_bytree': params['colsample_bytree'],
                    'seed': 42
                }
                num_boost_round = params['n_estimators']

                cv_results = xgb.cv(
                    current_params,
                    dtrain,
                    num_boost_round=num_boost_round,
                    nfold=nfold_cv, # Dynamicky nastavený počet foldov
                    metrics='mlogloss',
                    early_stopping_rounds=20,
                    verbose_eval=False,
                    seed=42
                )
                
                mean_logloss = cv_results['validation-mlogloss-mean'].min()
                
                if mean_logloss < best_logloss:
                    best_logloss = mean_logloss
                    best_params = current_params
                    best_params['n_estimators'] = cv_results['validation-mlogloss-mean'].argmin() + 1 # Uložíme optimálny počet kôl

                print(f"Parametre: {params}, Priemerný mlogloss (CV): {mean_logloss:.4f}")

            print(f"\nLadenie hyperparametrov dokončené. Najlepší mlogloss: {best_logloss:.4f}")
            print(f"Najlepšie hyperparametre pre XGBoost: {best_params}")

        # Trénovanie finálneho XGBoost modelu s najlepšími parametrami
        print("\nTrénujem finálny XGBoost model s najlepšími parametrami...")
        xgb_model = XGBoostModel(params=best_params)
        # Použijeme celý dataset pre finálne trénovanie, ak je dostatok dát
        # Inak by sme mohli použiť X_train, y_train ak by sme chceli zachovať validačnú sadu
        xgb_model.train(X, y, num_boost_round=best_params['n_estimators'], early_stopping_rounds=None) # Vypneme early stopping pre finálny model
        xgb_model.save(XGB_MODEL_PATH)
        joblib.dump(best_params, XGB_BEST_PARAMS_PATH) # Uložíme najlepšie parametre

    # --- Trénovanie Poisson modelu ---
    # Tento model stále môže používať pôvodné dáta na výpočet priemerov
    df_raw = pd.read_csv(RAW_DATA_PATH)
    poisson_model = PoissonModel()
    poisson_model.train(df_raw)
    poisson_model.save(POISSON_MODEL_PATH)

    print("\nTrénovanie bolo úspešne dokončené. Modely sú uložené.")

if __name__ == '__main__':
    # Vytvorenie priečinka pre modely, ak neexistuje
    if not os.path.exists(MODEL_DIR):
        os.makedirs(MODEL_DIR)
        
    train_and_save_models()

