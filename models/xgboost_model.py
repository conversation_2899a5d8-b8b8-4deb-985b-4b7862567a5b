import xgboost as xgb
import joblib
from sklearn.model_selection import train_test_split

class XGBoostModel:
    """
    Trieda pre XGBoost model na predikciu výsledku zápasu (výhra domácich, remíza, výhra hostí).
    """
    def __init__(self, params=None):
        if params is None:
            # Základné parametre pre multi-class klasifikáciu
            self.params = {
                'objective': 'multi:softprob',  # <PERSON><PERSON><PERSON><PERSON> na softprob pre vrátenie pravdepodobností
                'num_class': 3,  # 0: Away Win, 1: Draw, 2: Home Win
                'eval_metric': 'mlogloss',
                'eta': 0.1,
                'max_depth': 3,
                'seed': 42
            }
        else:
            self.params = params
        self.model = None

    def train(self, X, y, test_size=0.2, num_boost_round=500, early_stopping_rounds=20):
        """
        Trénuje model na trénovacej sade a vyhodnocuje na validačnej sade.
        Vzhľadom na malé množstvo dát je krížová validácia dočasne nahradená.
        """
        if len(X) < 5:
            print("Upozornenie: Nedostatok dát pre štandardné rozdelenie. Trénujem na všetkých dátach.")
            dtrain = xgb.DMatrix(X, label=y)
            self.model = xgb.train(
                self.params,
                dtrain,
                num_boost_round=50, # Znížený počet kôl pre malé dáta
                verbose_eval=False
            )
            print("XGBoost model bol úspešne natrénovaný na celom (malom) datasete.")
            return

        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=test_size, random_state=self.params.get('seed', 42))
        
        dtrain = xgb.DMatrix(X_train, label=y_train)
        dval = xgb.DMatrix(X_val, label=y_val)
        
        evals_result = {}
        print("Spúšťam trénovanie s validačnou sadou...")
        self.model = xgb.train(
            self.params,
            dtrain,
            num_boost_round=num_boost_round,
            evals=[(dtrain, 'train'), (dval, 'validation')],
            early_stopping_rounds=early_stopping_rounds,
            evals_result=evals_result,
            verbose_eval=False
        )
        
        final_mlogloss = evals_result['validation']['mlogloss'][-1]
        print(f"Trénovanie dokončené. Finálny mlogloss na validačnej sade: {final_mlogloss:.4f}")
        print(f"Optimálny počet kôl: {self.model.best_iteration}")

    def predict(self, X):
        """
        Vytvára predikcie pre nové dáta.
        """
        if self.model is None:
            raise ValueError("Model nie je natrénovaný. Zavolajte najprv metódu train().")
        
        dtest = xgb.DMatrix(X)
        return self.model.predict(dtest)

    def save(self, filepath):
        """
        Uloží natrénovaný model na zadané miesto.
        """
        joblib.dump(self.model, filepath)
        print(f"XGBoost model uložený do: {filepath}")

    @staticmethod
    def load(filepath):
        """
        Načíta model zo súboru.
        """
        model_loaded = joblib.load(filepath)
        model = XGBoostModel()
        model.model = model_loaded
        print(f"XGBoost model načítaný z: {filepath}")
        return model