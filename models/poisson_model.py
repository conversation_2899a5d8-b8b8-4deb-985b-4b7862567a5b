import pandas as pd
import numpy as np
from scipy.stats import poisson
import joblib

class PoissonModel:
    """
    Trieda pre Poissonov model, ktorý predikuje skóre zápasu
    na základe ofenzívnej a defenzívnej sily tímov.
    """
    def __init__(self):
        self.team_stats = None

    def train(self, data):
        """
        Vypočíta priemerný počet strelených a inkasovaných gólov pre každý tím.
        """
        # Výpočet priemerov pre domáce a vonkajšie zápasy
        home_stats = data.groupby('home_team').agg(avg_scored_home=('home_goals', 'mean'), avg_conceded_home=('away_goals', 'mean'))
        away_stats = data.groupby('away_team').agg(avg_scored_away=('away_goals', 'mean'), avg_conceded_away=('home_goals', 'mean'))

        # Spojenie štatistík do jedného DataFrame
        self.team_stats = pd.concat([home_stats, away_stats], axis=1).fillna(0)
        
        # Celkové priemery
        self.team_stats['avg_scored'] = (self.team_stats['avg_scored_home'] + self.team_stats['avg_scored_away']) / 2
        self.team_stats['avg_conceded'] = (self.team_stats['avg_conceded_home'] + self.team_stats['avg_conceded_away']) / 2
        
        print("Poisson model (štatistiky tímov) bol úspešne natrénovaný.")

    def predict_score(self, home_team, away_team):
        """
        Predikuje najpravdepodobnejšie skóre zápasu.
        """
        if self.team_stats is None:
            raise ValueError("Model nie je natrénovaný. Zavolajte najprv metódu train().")

        # Sila útoku a obrany
        home_attack_strength = self.team_stats.loc[home_team]['avg_scored']
        away_defense_strength = self.team_stats.loc[away_team]['avg_conceded']
        
        away_attack_strength = self.team_stats.loc[away_team]['avg_scored']
        home_defense_strength = self.team_stats.loc[home_team]['avg_conceded']

        # Očakávaný počet gólov (lambda)
        lambda_home = home_attack_strength * away_defense_strength
        lambda_away = away_attack_strength * home_defense_strength

        # Nájdenie najpravdepodobnejšieho skóre (max. 5 gólov)
        max_prob = -1
        best_score = (0, 0)
        for home_goals in range(6):
            for away_goals in range(6):
                prob = poisson.pmf(home_goals, lambda_home) * poisson.pmf(away_goals, lambda_away)
                if prob > max_prob:
                    max_prob = prob
                    best_score = (home_goals, away_goals)
        
        return best_score

    def save(self, filepath):
        """
        Ulo��í štatistiky tímu do súboru.
        """
        joblib.dump(self.team_stats, filepath)
        print(f"Poisson model (štatistiky) uložený do: {filepath}")

    @staticmethod
    def load(filepath):
        """
        Načíta štatistiky tímu zo súboru.
        """
        stats = joblib.load(filepath)
        model = PoissonModel()
        model.team_stats = stats
        print(f"Poisson model (štatistiky) načítaný z: {filepath}")
        return model