import pandas as pd
import os
import joblib
from models.xgboost_model import XGBoostModel
from models.poisson_model import PoissonModel

# Cesty k súborom
MODEL_DIR = 'data/trained_models'
PROCESSED_DATA_PATH = 'data/processed_features.csv'
XGB_MODEL_PATH = os.path.join(MODEL_DIR, 'xgboost_model.joblib')
POISSON_MODEL_PATH = os.path.join(MODEL_DIR, 'poisson_model.joblib')
SCALER_PATH = os.path.join(MODEL_DIR, 'scaler.joblib')

class Predictor:
    """
    Kombinuje predikcie z viacerých modelov na odhadnutie výsledkov zá<PERSON>ov.
    """
    def __init__(self):
        # Kontrola existencie súborov
        required_paths = [XGB_MODEL_PATH, POISSON_MODEL_PATH, PROCESSED_DATA_PATH, SCALER_PATH]
        if not all(os.path.exists(p) for p in required_paths):
            raise FileNotFoundError("<PERSON>ý<PERSON>j<PERSON> natrénované modely, scaler alebo dáta. Spustite najprv tréning.")
        
        # Načítanie modelov a scalera
        self.xgb_model = XGBoostModel.load(XGB_MODEL_PATH)
        self.poisson_model = PoissonModel.load(POISSON_MODEL_PATH)
        self.scaler = joblib.load(SCALER_PATH)
        
        # Načítanie ELO ratingov
        self.elo_ratings = self._get_latest_elo_ratings()
        
        print("Predictor bol úspešne inicializovaný s modelmi, scalerom a ELO ratingami.")

    def _get_latest_elo_ratings(self):
        """
        Načíta posledné známe ELO hodnoty pre každý tím.
        Poznámka: ELO v processed_features je už normalizované. Pre presnosť potrebujeme pôvodné.
        Pre jednoduchosť ich zatiaľ odhadneme z normalizovaných dát, ideálne by bolo ukladať ich zvlášť.
        """
        df = pd.read_csv(PROCESSED_DATA_PATH)
        # Toto je len aproximácia, keďže dáta sú škálované.
        # V ďalšej verzii by sa mali ELO ratingy ukladať pred škálovaním.
        elo_ratings = {}
        for _, row in df.iterrows():
            elo_ratings[row['home_team']] = row['elo_home'] 
            elo_ratings[row['away_team']] = row['elo_away']
        return elo_ratings

    def get_live_features(self, home_team, away_team):
        """
        Pripraví a normalizuje features pre aktuálny zápas.
        """
        team_stats = self.poisson_model.team_stats
        default_elo = 0 # V normalizovaných dátach je priemer 0
        default_avg = 1.0

        home_stats = team_stats.loc[home_team] if home_team in team_stats.index else None
        away_stats = team_stats.loc[away_team] if away_team in team_stats.index else None

        features_df = pd.DataFrame([{
            'home_avg_scored': home_stats['avg_scored_home'] if home_stats is not None else default_avg,
            'home_avg_conceded': home_stats['avg_conceded_home'] if home_stats is not None else default_avg,
            'away_avg_scored': away_stats['avg_scored_away'] if away_stats is not None else default_avg,
            'away_avg_conceded': away_stats['avg_conceded_away'] if away_stats is not None else default_avg,
            'form_home': 1,  # Placeholder
            'form_away': 1,  # Placeholder
            'elo_home': self.elo_ratings.get(home_team, default_elo),
            'elo_away': self.elo_ratings.get(away_team, default_elo)
        }])
        
        # Normalizácia features pomocou načítaného scalera
        scaled_features = self.scaler.transform(features_df)
        
        return pd.DataFrame(scaled_features, columns=features_df.columns)

    def predict_probabilities(self, home_team, away_team):
        """
        Vytvorí predikciu pravdepodobností pre výsledok zápasu.
        """
        features_df = self.get_live_features(home_team, away_team)
        probabilities = self.xgb_model.predict(features_df)
        
        prob_away_win, prob_draw, prob_home_win = probabilities[0]
        
        return {
            'prob_home_win': prob_home_win,
            'prob_draw': prob_draw,
            'prob_away_win': prob_away_win
        }

    def predict_score(self, home_team, away_team):
        """
        Predikuje presné skóre pomocou Poissonovho modelu.
        """
        return self.poisson_model.predict_score(home_team, away_team)

# Príklad použitia
if __name__ == '__main__':
    try:
        predictor = Predictor()
        home = 'Team A'
        away = 'Team C'
        
        probs = predictor.predict_probabilities(home, away)
        score = predictor.predict_score(home, away)
        
        print(f"\nPredikcia pre zápas: {home} vs {away}")
        print(f"Odhadované pravdepodobnosti: Domáci {probs['prob_home_win']*100:.1f}%, Remíza {probs['prob_draw']*100:.1f}%, Hostia {probs['prob_away_win']*100:.1f}%")
        print(f"Odhadované skóre: {score[0]}:{score[1]}")

    except FileNotFoundError as e:
        print(e)
    except KeyError as e:
        print(f"Chyba: Tím '{e.args[0]}' nebol nájdený v historických dátach.")
