from analysis.predictor import Predictor

def calculate_value(model_probability, bookmaker_probability):
    """
    Vypočíta "value" stávky porovnaním pravdepodobnosti modelu a bookmakera.
    Value = (pravdepodobnosť modelu / pravdepodobnosť bookmakera) - 1
    """
    if bookmaker_probability is None or model_probability is None or bookmaker_probability == 0:
        return 0.0
    return (model_probability / bookmaker_probability) - 1

def calculate_overround(odds):
    """
    Vypočíta maržu (overround) bookmakera z poskytnutých kurzov.
    """
    if not all(o > 0 for o in odds):
        return 0.0
    return sum(1 / o for o in odds)

def get_fair_odds(odds_list):
    """
    Odstráni maržu z kurzov a vráti "férové" kurzy a pravdepodobnosti.
    """
    overround = calculate_overround(odds_list)
    if overround == 0:
        return odds_list, [0.0] * len(odds_list)
    
    fair_probs = [(1 / o) / overround for o in odds_list]
    fair_odds = [1 / p if p > 0 else float('inf') for p in fair_probs]
    
    return fair_odds, fair_probs

def analyze_value(odds_data):
    """
    Analyzuje dáta o kurzoch, vypočíta maržu, férové pravdepodobnosti
    a porovná ich s predikciami modelu na nájdenie value.
    """
    try:
        predictor = Predictor()
    except FileNotFoundError as e:
        print(f"Chyba pri inicializácii Predictora: {e}. Skúste najprv spustiť tréning.")
        return []

    results = []
    for match in odds_data:
        home_team, away_team = match['home_team'], match['away_team']
        bookmaker = match['bookmakers'][0]
        market = next((m for m in bookmaker['markets'] if m['key'] == 'h2h'), None)
        
        if not market:
            continue

        home_odds = next((o['price'] for o in market['outcomes'] if o['name'] == home_team), None)
        away_odds = next((o['price'] for o in market['outcomes'] if o['name'] == away_team), None)
        draw_odds = next((o['price'] for o in market['outcomes'] if o['name'] == 'Draw'), None)

        if not all([home_odds, away_odds, draw_odds]):
            continue

        # Výpočet marže a férových pravdepodobností
        bookmaker_odds = [home_odds, draw_odds, away_odds]
        overround = calculate_overround(bookmaker_odds)
        _, bookmaker_probs = get_fair_odds(bookmaker_odds)
        prob_b_home, prob_b_draw, prob_b_away = bookmaker_probs

        # Získanie predikcií z nášho modelu
        try:
            model_probs = predictor.predict_probabilities(home_team, away_team)
        except KeyError as e:
            print(f"Preskakujem zápas: Tím '{e.args[0]}' nebol nájdený v tréningových dátach.")
            continue

        # Výpočet value porovnaním pravdepodobností
        value_home = calculate_value(model_probs['prob_home_win'], prob_b_home)
        value_away = calculate_value(model_probs['prob_away_win'], prob_b_away)
        value_draw = calculate_value(model_probs['prob_draw'], prob_b_draw)

        results.append({
            'home_team': home_team, 'away_team': away_team,
            'prob_home_win': model_probs['prob_home_win'],
            'prob_draw': model_probs['prob_draw'],
            'prob_away_win': model_probs['prob_away_win'],
            'home_odds': home_odds, 'draw_odds': draw_odds, 'away_odds': away_odds,
            'value_home': value_home, 'value_away': value_away, 'value_draw': value_draw,
            'bookmaker_margin': overround - 1
        })

    return results
