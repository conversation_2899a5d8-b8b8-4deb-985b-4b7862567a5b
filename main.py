# ==========================================================
# TO-DO CHECKLIST – ĎALŠÍ SPRINT (verzia C → D)
# ==========================================================
# 1. Vylepšiť features:
#    - Pridať ELO hodnoty do XGBoost modelu
#    - Rozšíriť data_preprocessing o priemerné góly doma/vonku
#    - Normalizovať features (min-max alebo standard scaler)
#
# 2. Optimalizovať modely:
#    - Cross-validation pre XGBoost
#    - Ukladať metriky tréningu (accuracy, log-loss)
#
# 3. Vylepšiť value bet analýzu:
#    - Zohľadniť margin bookmakerov pri výpočte pravdepodobností
#    - Zobraziť odporúčanú stávku podľa Kellyho kritéria
#
# 4. Vizualizácia & integrácia:
#    - Pridať základ pre Telegram bota (notifier.py)
#    - Začať Streamlit dashboard (dashboard/app.py) pre grafy
#    - Exportovať dáta do JSON pre vizualizáciu
#
# 5. Automatizácia:
#    - Naplánovať denné spúšťanie tréningu a zber dát (cron/APS)
#
# ==========================================================

import os
import subprocess
from scraping.odds_api import fetch_odds
from analysis.value_bet import analyze_value
from bankroll.kelly import kelly_bet

# Cesty k modelom - opravené koncovky
POISSON_MODEL_PATH = "data/trained_models/poisson_model.joblib"
XGB_MODEL_PATH = "data/trained_models/xgboost_model.joblib"


def ensure_models():
    """
    Skontroluje, či existujú modely; ak nie, spustí tréning
    """
    if not (os.path.exists(POISSON_MODEL_PATH) and os.path.exists(XGB_MODEL_PATH)):
        print("Modely chýbajú – spúšťam tréning pipeline...")
        # Oprava spustenia tréningu ako modulu
        subprocess.run(["python", "-m", "models.trainer"], check=True)
    else:
        print("Modely nájdené – pokračujem v analýze.")


def main():
    # Definovanie bankrollu
    bankroll = 100  # Príklad: 100 EUR
    value_threshold = 0.05 # Minimálna value, ktorú považujeme za zaujímavú (5%)

    # Skontrolovať a prípadne natrénovať modely
    ensure_models()

    # Načítať kurzy z API
    print("Načítavam kurzy a analyzujem value bety...")
    odds_data = fetch_odds()

    # Analyzovať value bety
    results = analyze_value(odds_data)

    # Výstup
    print("-" * 50)
    for match in results:
        print(f"\n{match['home_team']} vs {match['away_team']}")
        print(f"  Kurzy: D:{match['home_odds']:.2f}, R:{match['draw_odds']:.2f}, H:{match['away_odds']:.2f} | Marža: {match['bookmaker_margin']*100:.2f}%")
        print(f"  Model P(D/R/H): {match['prob_home_win']*100:.1f}% / {match['prob_draw']*100:.1f}% / {match['prob_away_win']*100:.1f}%")
        
        # Logika pre odporúčanie stávky
        tip_found = False
        if match['value_home'] > value_threshold:
            stake = kelly_bet(bankroll, match['prob_home_win'], match['home_odds'])
            print(f"  -> TIP (Value: {match['value_home']:.2%}): Bet na domácich | Stávka: {stake:.2f} EUR")
            tip_found = True
        
        if match['value_away'] > value_threshold:
            stake = kelly_bet(bankroll, match['prob_away_win'], match['away_odds'])
            print(f"  -> TIP (Value: {match['value_away']:.2%}): Bet na hostí | Stávka: {stake:.2f} EUR")
            tip_found = True
        
        if not tip_found:
            print("  X Žiadny value bet")
    print("-" * 50)


if __name__ == "__main__":
    main()
