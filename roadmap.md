# Betting Agent AI – Roadmap ďalšieho vývoja

## Stav projektu

Pipeline pre zber dát, spraco<PERSON><PERSON> d<PERSON>t (forma + ELO) a tréning modelov (Poisson, XGBoost) je pripravená.

Základná logika value bet analýzy a CLI výstupu je funkčná.

Projekt je pripravený na rozširovanie o ďalšie moduly a integrácie (Telegram, dashboard).

## Ciele ďalšieho vývoja

### Rozšírenie dát a featuringu

- Pridať viac historických dát (rozličné ligy, sezóny).
- Doplnkové features: do<PERSON><PERSON><PERSON>/vonku štatistiky, xG (expected goals), <PERSON><PERSON><PERSON><PERSON> hry, formácia tímov.
- Rozšíriť ELO o adaptívny K faktor podľa dôležitosti zápasu.

### Modely a predikcia

- Optimalizácia Poisson modelu pre predikciu skóre oboch tímov.
- XGBoost: pridať ELO hodnoty ako feature.
- Testovať ensemble (kombinovať Poisson + XGBoost + historický priemer).
- Implementovať cross-validation pre hodnotenie presnosti.

### Value bet analýza

- Dynamické prahové hodnoty pre identifikáciu value betov.
- Zohľadniť margin bookmakerov (overround) pri výpočtoch.
- Pridať simuláciu výnosnosti stratégií (backtesting).

### Bankroll management

- Implementovať Kellyho kritérium (už hotové) a rozšíriť o frakčný Kelly (napr. 50 %).
- Pridať možnosť správy viacerých bankrollov (napr. pre rôzne ligy).

### Integrácia a vizualizácia

- Telegram bot: zasielanie tipov v reálnom čase.
- Web dashboard (Flask alebo Streamlit):
  - prehľad zápasov, kurzov a value betov,
  - grafy vývoja bankrollu, ELO tímov a ROI stratégie.

### Automatizácia

- Plánovač (cron / APScheduler) na denné sťahovanie dát a automatický tréning.
- Možnosť bežať ako Docker kontajner pre jednoduché nasadenie.

---

## Konkrétne úlohy (ďalší sprint)

### 1. Vylepšenie featuringu

- Pridať ELO hodnoty do XGBoost modelu (nový feature).
- Rozšíriť data_preprocessing.py o priemerné góly doma/vonku.
- Implementovať funkciu na normalizáciu dát pred tréningom.

### 2. Optimalizácia modelov

- Upraviť trainer.py na cross-validation pri tréningu XGBoost.
- Vyhodnotiť presnosť modelov a ukladať metriky (accuracy, log-loss).

### 3. Value bet vylepšenia

- Pridať výpočet marginu bookmakerov a upraviť pravdepodobnosti.
- Zobraziť value bety aj s odporúčanou stávkou podľa Kellyho.

### 4. Vizualizácie a integrácia

- Vytvoriť základ pre Telegram bota (telegram_bot/notifier.py).
- Vytvoriť základný dashboard v Streamlit (dashboard/app.py).
- Funkcia exportu dát do JSON pre vizualizáciu.

---

## Budúce smery (verzia D/E)

- Rozšíriť na viac športov (basketbal, hokej).
- Využiť pokročilé modely (napr. neurónové siete – LSTM pre sekvenčné dáta).
- Live betting – spracovanie dát počas zápasu.
- Simulátor stratégie – testovanie rôznych stávkových stratégií na historických dátach.

## Štandardy kódu

- PEP8 + typové anotácie (Python typing).
- Modularita – každý modul samostatne testovateľný.
- Používať joblib na uloženie modelov, pandas pre dáta, xgboost pre klasifikáciu.
- Testovať pipeline: načítanie → spracovanie → tréning → predikcia → value bet.
