# Football Data Scraping

Tento adresár obsahuje moduly pre získavanie futbalových dát z rôznych API zdrojov.

## Moduly

### 1. odds_api.py
Scraper pre The-Odds-API na získavanie aktuálnych kurzov.

### 2. football_data_scraper.py
Scraper pre Football-Data.org API na získavanie historických futbalových dát.

## Football-Data.org API Setup

### Krok 1: Registrácia
1. Navštívte https://www.football-data.org/
2. Zaregistrujte sa pre bezplatný účet
3. Získajte API kľúč z vášho dashboard

### Krok 2: Konfigurácia
1. Otvorte `config.py` v root adresári
2. Nastavte `API_KEY_FOOTBALL_DATA` na váš API kľúč:
```python
API_KEY_FOOTBALL_DATA = "váš_api_kľúč_tu"
```

### Krok 3: Použitie

#### Spustenie cez main.py (odporúčané)
```bash
python main.py --historical
```

#### Priame spustenie scrapera
```bash
python scraping/football_data_scraper.py
```

#### Programové použitie
```python
from scraping.football_data_scraper import FootballDataScraper

scraper = FootballDataScraper()

# Stiahnutie zápasov pre konkrétnu súťaž a sezónu
matches = scraper.fetch_competition_matches("PL", 2023)  # Premier League 2023

# Transformácia do CSV formátu
csv_data = scraper.transform_matches_to_csv_format(matches)

# Uloženie do súboru
scraper.save_historical_data(csv_data, "data/premier_league_2023.csv")
```

## Dostupné súťaže

Scraper podporuje nasledovné súťaže (konfigurovateľné v `config.py`):

| Kód | Súťaž | Krajina |
|-----|-------|---------|
| PL | Premier League | Anglicko |
| BL1 | Bundesliga | Nemecko |
| SA | Serie A | Taliansko |
| PD | Primera Division (La Liga) | Španielsko |
| FL1 | Ligue 1 | Francúzsko |
| DED | Eredivisie | Holandsko |
| PPL | Primeira Liga | Portugalsko |

## Sezóny

Predvolene sa sťahujú sezóny: 2020, 2021, 2022, 2023
(konfigurovateľné v `FOOTBALL_DATA_SEASONS`)

## Výstupný formát

Scraper generuje CSV súbor s nasledovnými stĺpcami:
- `date`: Dátum zápasu (YYYY-MM-DD)
- `home_team`: Názov domáceho tímu
- `away_team`: Názov hosťujúceho tímu
- `home_goals`: Počet gólov domáceho tímu
- `away_goals`: Počet gólov hosťujúceho tímu
- `home_odds`: Kurz na výhru domácich (placeholder)
- `draw_odds`: Kurz na remízu (placeholder)
- `away_odds`: Kurz na výhru hostí (placeholder)

**Poznámka o kurzoch**: Football-Data.org API neposkytuje historické kurzy, preto scraper generuje realistické placeholder hodnoty založené na výsledku zápasu.

## Rate Limiting

Football-Data.org API má nasledovné limity:
- **Free tier**: 10 requestov za minútu
- Scraper automaticky dodržiava tieto limity s 6-sekundovými pauzami medzi requestmi

## Chybové stavy

Scraper zvláda nasledovné situácie:
- Rate limiting (automatické čakanie)
- Sieťové chyby (retry mechanizmus)
- Neplatné dáta (preskočenie)
- Chýbajúce API kľúče (varovanie)

## Príklady použitia

### Stiahnutie len Premier League
```python
scraper = FootballDataScraper()
matches = scraper.scrape_multiple_competitions(
    competitions=["PL"], 
    seasons=[2023]
)
scraper.save_historical_data(matches, "data/pl_2023.csv")
```

### Stiahnutie viacerých líg
```python
scraper = FootballDataScraper()
matches = scraper.scrape_multiple_competitions(
    competitions=["PL", "BL1", "SA"], 
    seasons=[2022, 2023]
)
scraper.save_historical_data(matches)
```

## Integrácia s existujúcim systémom

Scraper je plne kompatibilný s existujúcim `data_preprocessing.py` modulom. Po stiahnutí historických dát môžete spustiť:

```bash
python -m data.data_preprocessing
python -m models.trainer
```

Alebo použiť `main.py --historical` ktorý automaticky spustí celý pipeline.
