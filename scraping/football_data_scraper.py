"""
Football-Data.org API scraper pre získavanie historických futbalových dát.

Tento modul umožňuje sťahovanie historických zápasov z rôznych európskych líg
pomocou Football-Data.org API a ich transformáciu do formátu kompatibilného
s existujúcim systémom.

Použitie:
    python scraping/football_data_scraper.py

Pred použitím:
    1. Zaregistrujte sa na https://www.football-data.org/
    2. Získajte API kľúč
    3. Nastavte API_KEY_FOOTBALL_DATA v config.py
"""

import requests
import pandas as pd
import time
import random
from datetime import datetime
from typing import List, Dict, Optional
import os
import sys

# Pridanie root adresára do Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    API_KEY_FOOTBALL_DATA, 
    FOOTBALL_DATA_BASE_URL,
    FOOTBALL_DATA_COMPETITIONS,
    FOOTBALL_DATA_SEASONS,
    DEFAULT_ODDS
)


class FootballDataScraper:
    """Scraper pre Football-Data.org API."""
    
    def __init__(self, api_key: str = API_KEY_FOOTBALL_DATA):
        """
        Inicializácia scrapera.
        
        Args:
            api_key: API kľúč pre Football-Data.org
        """
        self.api_key = api_key
        self.base_url = FOOTBALL_DATA_BASE_URL
        self.headers = {
            "X-Auth-Token": self.api_key,
            "User-Agent": "BettingAgent/1.0"
        }
        self.rate_limit_delay = 6  # 10 requests/minute = 6 sekúnd medzi requestmi
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Vykonanie HTTP requestu s error handlingom a rate limitingom.
        
        Args:
            endpoint: API endpoint (napr. "/competitions/PL/matches")
            params: Query parametre
            
        Returns:
            JSON response alebo None pri chybe
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            print(f"Volám API: {url}")
            response = requests.get(url, headers=self.headers, params=params)
            
            # Rate limiting
            time.sleep(self.rate_limit_delay)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                print("Rate limit dosiahnutý. Čakám 60 sekúnd...")
                time.sleep(60)
                return self._make_request(endpoint, params)
            elif response.status_code == 403:
                print(f"Prístup zamietnutý (403) - súťaž/sezóna vyžaduje platený plán")
                return None
            else:
                print(f"API chyba: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Request chyba: {e}")
            return None
    
    def fetch_competition_matches(self, competition_code: str, season: int) -> List[Dict]:
        """
        Získanie zápasov pre konkrétnu súťaž a sezónu.
        
        Args:
            competition_code: Kód súťaže (napr. "PL", "BL1")
            season: Rok začiatku sezóny (napr. 2021)
            
        Returns:
            Zoznam zápasov
        """
        endpoint = f"/competitions/{competition_code}/matches"
        params = {
            "season": season,
            "status": "FINISHED"  # Len dokončené zápasy
        }
        
        data = self._make_request(endpoint, params)
        if data and "matches" in data:
            print(f"Získané {len(data['matches'])} zápasov pre {competition_code} sezóna {season}")
            return data["matches"]
        else:
            print(f"Nepodarilo sa získať zápasy pre {competition_code} sezóna {season}")
            return []
    
    def _generate_realistic_odds(self, home_team: str, away_team: str,
                               home_goals: int, away_goals: int) -> Dict[str, float]:
        """
        Generovanie realistických placeholder kurzov s korektnou pravdepodobnosťou.

        Args:
            home_team: Názov domáceho tímu
            away_team: Názov hosťujúceho tímu
            home_goals: Góly domáceho tímu
            away_goals: Góly hosťujúceho tímu

        Returns:
            Dictionary s kurzami
        """
        # Základné pravdepodobnosti (súčet = 1.0)
        if home_goals > away_goals:  # Domáci vyhral
            prob_home = random.uniform(0.45, 0.65)
            prob_draw = random.uniform(0.20, 0.30)
            prob_away = 1.0 - prob_home - prob_draw
        elif away_goals > home_goals:  # Hosťujúci vyhral
            prob_away = random.uniform(0.45, 0.65)
            prob_draw = random.uniform(0.20, 0.30)
            prob_home = 1.0 - prob_away - prob_draw
        else:  # Remíza
            prob_draw = random.uniform(0.35, 0.45)
            prob_home = random.uniform(0.25, 0.35)
            prob_away = 1.0 - prob_home - prob_draw

        # Pridanie bookmaker marže (5-10%)
        margin = random.uniform(1.05, 1.10)

        # Konverzia na kurzy s maržou
        home_odds = round(margin / prob_home, 2)
        draw_odds = round(margin / prob_draw, 2)
        away_odds = round(margin / prob_away, 2)

        # Zabezpečenie minimálnych kurzov
        home_odds = max(1.10, home_odds)
        draw_odds = max(2.50, draw_odds)
        away_odds = max(1.10, away_odds)

        return {
            "home_odds": home_odds,
            "draw_odds": draw_odds,
            "away_odds": away_odds
        }
    
    def transform_matches_to_csv_format(self, matches: List[Dict]) -> List[Dict]:
        """
        Transformácia zápasov do formátu CSV.

        Args:
            matches: Zoznam zápasov z API

        Returns:
            Zoznam riadkov pre CSV
        """
        csv_rows = []
        skipped_count = 0

        for match in matches:
            # Debug: pozrime si štruktúru prvého zápasu
            if len(csv_rows) == 0 and len(matches) > 0:
                print(f"Debug - štruktúra zápasu: {list(match.keys())}")
                if match.get("score"):
                    print(f"Debug - score štruktúra: {list(match['score'].keys())}")
                    if match["score"].get("fullTime"):
                        print(f"Debug - fullTime štruktúra: {list(match['score']['fullTime'].keys())}")

            # Kontrola či má zápas všetky potrebné dáta
            if (not match.get("score") or
                not match["score"].get("fullTime") or
                not match["score"]["fullTime"].get("homeTeam") or
                not match["score"]["fullTime"].get("awayTeam") or
                match["score"]["fullTime"]["homeTeam"] is None or
                match["score"]["fullTime"]["awayTeam"] is None):
                skipped_count += 1
                continue
            
            # Extrakcia dát
            date_str = match["utcDate"][:10]  # YYYY-MM-DD
            home_team = match["homeTeam"]["name"]
            away_team = match["awayTeam"]["name"]
            home_goals = match["score"]["fullTime"]["homeTeam"]
            away_goals = match["score"]["fullTime"]["awayTeam"]
            
            # Generovanie kurzov
            odds = self._generate_realistic_odds(home_team, away_team, home_goals, away_goals)
            
            csv_row = {
                "date": date_str,
                "home_team": home_team,
                "away_team": away_team,
                "home_goals": home_goals,
                "away_goals": away_goals,
                "home_odds": odds["home_odds"],
                "draw_odds": odds["draw_odds"],
                "away_odds": odds["away_odds"]
            }
            
            csv_rows.append(csv_row)
        
        return csv_rows
    
    def save_historical_data(self, csv_data: List[Dict], filename: str = "data/raw_results.csv"):
        """
        Uloženie dát do CSV súboru.
        
        Args:
            csv_data: Dáta na uloženie
            filename: Názov súboru
        """
        if not csv_data:
            print("Žiadne dáta na uloženie.")
            return
        
        df = pd.DataFrame(csv_data)
        
        # Zoradenie podľa dátumu
        df["date"] = pd.to_datetime(df["date"])
        df = df.sort_values("date")
        df["date"] = df["date"].dt.strftime("%Y-%m-%d")
        
        # Vytvorenie adresára ak neexistuje
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # Uloženie
        df.to_csv(filename, index=False)
        print(f"Uložené {len(df)} zápasov do {filename}")
    
    def scrape_multiple_competitions(self, competitions: List[str] = None, 
                                   seasons: List[int] = None) -> List[Dict]:
        """
        Stiahnutie dát pre viacero súťaží a sezón.
        
        Args:
            competitions: Zoznam kódov súťaží
            seasons: Zoznam sezón
            
        Returns:
            Všetky zápasy
        """
        if competitions is None:
            competitions = list(FOOTBALL_DATA_COMPETITIONS.keys())
        if seasons is None:
            seasons = FOOTBALL_DATA_SEASONS
        
        all_matches = []
        
        for competition in competitions:
            print(f"\nSpracúvam súťaž: {competition} ({FOOTBALL_DATA_COMPETITIONS.get(competition, 'Neznáma')})")
            
            for season in seasons:
                print(f"  Sezóna: {season}")
                matches = self.fetch_competition_matches(competition, season)
                csv_data = self.transform_matches_to_csv_format(matches)
                all_matches.extend(csv_data)
        
        return all_matches


def main():
    """Hlavná funkcia pre spustenie scrapera."""
    print("Football-Data.org Historical Data Scraper")
    print("=" * 50)
    
    # Kontrola API kľúča
    if API_KEY_FOOTBALL_DATA == "YOUR_FOOTBALL_DATA_API_KEY":
        print("CHYBA: Nastavte API_KEY_FOOTBALL_DATA v config.py")
        print("Zaregistrujte sa na https://www.football-data.org/ a získajte API kľúč")
        return
    
    scraper = FootballDataScraper()
    
    # Stiahnutie dát
    print("Sťahujem historické dáta...")
    all_matches = scraper.scrape_multiple_competitions()
    
    if all_matches:
        # Uloženie do CSV
        scraper.save_historical_data(all_matches)
        print(f"\nÚspešne stiahnuté a uložené {len(all_matches)} zápasov!")
    else:
        print("Nepodarilo sa získať žiadne dáta.")


if __name__ == "__main__":
    main()
