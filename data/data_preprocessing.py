import pandas as pd
import numpy as np
import os
import joblib
from sklearn.preprocessing import StandardScaler

RAW_DATA_PATH = "data/raw_results.csv"
PROCESSED_DATA_PATH = "data/processed_features.csv"
SCALER_PATH = "data/trained_models/scaler.joblib"

def calculate_form(goals_for, goals_against):
    """Zjednodušená forma: 2 body za výhru, 1 za remízu, 0 za prehru"""
    if goals_for > goals_against:
        return 2
    elif goals_for == goals_against:
        return 1
    else:
        return 0

def calculate_elo(home_rating, away_rating, home_goals, away_goals, k=20):
    """
    Jednoduchý ELO rating update
    """
    expected_home = 1 / (1 + 10 ** ((away_rating - home_rating) / 400))
    score_home = 1 if home_goals > away_goals else 0.5 if home_goals == away_goals else 0
    new_home = home_rating + k * (score_home - expected_home)
    new_away = away_rating + k * ((1 - score_home) - (1 - expected_home))
    return new_home, new_away

def preprocess_data():
    """
    Spracuje raw_results.csv, normalizuje features a uloží ich do processed_features.csv.
    Zároveň uloží použitý scaler.
    """
    if not os.path.exists(RAW_DATA_PATH):
        raise FileNotFoundError(f"Chýba {RAW_DATA_PATH}. Pridaj historické dáta.")

    df = pd.read_csv(RAW_DATA_PATH)

    # Konvertovať stĺpce gólov na numerické, neplatné hodnoty nastaviť na NaN
    df['home_goals'] = pd.to_numeric(df['home_goals'], errors='coerce')
    df['away_goals'] = pd.to_numeric(df['away_goals'], errors='coerce')

    # Vynechať riadky, kde sú góly NaN (ak by sa vyskytli po konverzii)
    df.dropna(subset=['home_goals', 'away_goals'], inplace=True)

    # --- Výpočet štatistík pre tímy ---
    home_stats = df.groupby('home_team').agg(
        avg_scored_home=('home_goals', 'mean'),
        avg_conceded_home=('away_goals', 'mean')
    ).reset_index().rename(columns={'home_team': 'team'})

    away_stats = df.groupby('away_team').agg(
        avg_scored_away=('away_goals', 'mean'),
        avg_conceded_away=('home_goals', 'mean')
    ).reset_index().rename(columns={'away_team': 'team'})
    
    team_stats = pd.merge(home_stats, away_stats, on='team', how='outer').fillna(0)

    # --- Iterácia a vytváranie features ---
    elo_ratings = {}
    default_elo = 1500
    processed_rows = []
    for i, row in df.iterrows():
        home_team, away_team = row['home_team'], row['away_team']
        home_goals, away_goals = row['home_goals'], row['away_goals']
        
        home_elo, away_elo = elo_ratings.get(home_team, default_elo), elo_ratings.get(away_team, default_elo)
        
        # Zabezpečiť, aby team_stats obsahoval záznamy pre aktuálne tímy
        home_team_stats = team_stats[team_stats['team'] == home_team]
        away_team_stats = team_stats[team_stats['team'] == away_team]

        if home_team_stats.empty or away_team_stats.empty:
            # Ak chýbajú štatistiky pre tím, preskočiť tento riadok alebo použiť defaultné hodnoty
            # Pre jednoduchosť teraz preskočíme, ale v reálnom scenári by sa mohla použiť imputácia
            print(f"Upozornenie: Chýbajú štatistiky pre {home_team} alebo {away_team}. Riadok preskočený.")
            continue

        home_team_stats = home_team_stats.iloc[0]
        away_team_stats = away_team_stats.iloc[0]
        
        new_home_elo, new_away_elo = calculate_elo(home_elo, away_elo, home_goals, away_goals)
        elo_ratings[home_team], elo_ratings[away_team] = new_home_elo, new_away_elo

        processed_rows.append({
            "home_team": home_team, "away_team": away_team,
            "home_avg_scored": home_team_stats['avg_scored_home'],
            "home_avg_conceded": home_team_stats['avg_conceded_home'],
            "away_avg_scored": away_team_stats['avg_scored_away'],
            "away_avg_conceded": away_team_stats['avg_conceded_away'],
            "form_home": calculate_form(home_goals, away_goals),
            "form_away": calculate_form(away_goals, home_goals),
            "elo_home": new_home_elo, "elo_away": new_away_elo,
            "result": 2 if home_goals > away_goals else 1 if home_goals == away_goals else 0,
            "home_goals": home_goals, "away_goals": away_goals
        })

    processed_df = pd.DataFrame(processed_rows)
    
    # Vynechať riadky s NaN v kľúčových stĺpcoch po vytvorení príznakov
    # Toto je dôležité pre StandardScaler a XGBoost
    features_and_target = [
        'home_avg_scored', 'home_avg_conceded', 'away_avg_scored', 'away_avg_conceded',
        'form_home', 'form_away', 'elo_home', 'elo_away', 'result'
    ]
    processed_df.dropna(subset=features_and_target, inplace=True)

    # --- Normalizácia features ---
    features_to_scale = [
        'home_avg_scored', 'home_avg_conceded', 'away_avg_scored', 'away_avg_conceded',
        'form_home', 'form_away', 'elo_home', 'elo_away'
    ]
    
    # Skontrolovať, či sú dáta na škálovanie prázdne
    if processed_df[features_to_scale].empty:
        print("Upozornenie: Žiadne dáta na škálovanie po predspracovaní. Skript sa ukončí.")
        return # Ukončiť funkciu, ak nie sú dáta

    scaler = StandardScaler()
    processed_df[features_to_scale] = scaler.fit_transform(processed_df[features_to_scale])
    
    # --- Uloženie dát a scalera ---
    os.makedirs(os.path.dirname(PROCESSED_DATA_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(SCALER_PATH), exist_ok=True)
    
    processed_df.to_csv(PROCESSED_DATA_PATH, index=False)
    joblib.dump(scaler, SCALER_PATH)
    print(f"Scaler uložený do: {SCALER_PATH}")

    return processed_df # Vrátiť spracovaný DataFrame

if __name__ == '__main__':
    print("Spúšťam predspracovanie a normalizáciu dát...")
    df_processed = preprocess_data()
    if df_processed is not None:
        print(f"Dáta boli úspešne spracované a uložené do {PROCESSED_DATA_PATH}")

