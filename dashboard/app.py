import streamlit as st
import pandas as pd
import os
import sys

# Pridanie cesty k hlavnému adresáru projektu, aby sme mohli importovať moduly
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from analysis.value_bet import analyze_value
from scraping.odds_api import fetch_odds

def main():
    """
    Hlavná funkcia Streamlit aplikácie.
    """
    st.set_page_config(page_title="Betting Agent AI", layout="wide")
    
    st.title("Betting Agent AI Dashboard")
    st.write("Vitajte v dashboarde pre analýzu stávkových príležitostí.")

    # --- Sidebar ---
    st.sidebar.header("Nastavenia")
    bankroll = st.sidebar.number_input("Veľkosť bankrollu (EUR)", min_value=10, max_value=10000, value=100, step=10)
    value_threshold = st.sidebar.slider("Minimálna 'value' pre zobrazenie tipu (%)", 0, 50, 5) / 100.0

    # --- <PERSON>lav<PERSON><PERSON> obsah ---
    st.header("Aktuálne Value Bety")

    if st.button("Načítať a analyzovať dáta"):
        with st.spinner("Načítavam kurzy a hľadám value bety..."):
            try:
                odds_data = fetch_odds()
                results = analyze_value(odds_data)
                
                if not results:
                    st.warning("Žiadne dáta o kurzoch neboli nájdené alebo analyzované.")
                    return

                df = pd.DataFrame(results)
                
                # Filter for value bets
                value_bets_df = df[
                    (df['value_home'] > value_threshold) |
                    (df['value_away'] > value_threshold) |
                    (df['value_draw'] > value_threshold)
                ]

                if not value_bets_df.empty:
                    st.success("Dáta boli úspešne načítané a analyzované!")
                    st.dataframe(value_bets_df)
                    st.info(f"Zobrazujú sa tipy s value > {value_threshold*100:.0f}%")
                else:
                    st.info(f"Žiadne value bety neboli nájdené s prahom > {value_threshold*100:.0f}%")

            except Exception as e:
                st.error(f"Nastala chyba pri načítavaní alebo analýze dát: {e}")

if __name__ == "__main__":
    main()
