import streamlit as st
import pandas as pd
import joblib
import os
import sys

# Pridanie cesty k hlavnému adres<PERSON>ru projektu, aby sme mohli importovať moduly
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from analysis.value_bet import analyze_value
from analysis.predictor import Predictor # Importujeme Predictor
from scraping.odds_api import fetch_odds
from bankroll.kelly import kelly_bet
from datetime import datetime, timedelta

RAW_DATA_PATH = 'data/raw_results.csv' # Cesta k surovým dátam

def main():
    """
    Hlavná funkcia Streamlit aplikácie.
    """
    st.set_page_config(page_title="Betting Agent AI", layout="wide")
    
    st.title("Betting Agent AI Dashboard")
    st.write("Vitajte v dashboarde pre analýzu stávkových príležitostí.")

    # Načítanie zoznamu tímov pre selectboxy
    all_teams = []
    if os.path.exists(RAW_DATA_PATH):
        df_raw = pd.read_csv(RAW_DATA_PATH)
        all_teams = sorted(list(pd.concat([df_raw['home_team'], df_raw['away_team']]).unique()))
    else:
        st.warning("Súbor raw_results.csv nebol nájdený. Zoznam tímov nebude k dispozícii.")

    # --- Sidebar ---
    st.sidebar.header("Nastavenia")
    bankroll = st.sidebar.number_input("Veľkosť bankrollu (EUR)", min_value=10, max_value=10000, value=100, step=10)
    value_threshold = st.sidebar.slider("Minimálna 'value' pre zobrazenie tipu (%)", 0, 50, 5) / 100.0

    # --- Sekcia: Predikcia zápasu ---
    st.header("Predikcia zápasu")
    if all_teams:
        col1, col2 = st.columns(2)
        with col1:
            home_team_pred = st.selectbox("Domáci tím", all_teams, index=0)
        with col2:
            # Zabezpečiť, aby away_team nebol rovnaký ako home_team
            away_teams_for_select = [team for team in all_teams if team != home_team_pred]
            away_team_pred = st.selectbox("Hosťujúci tím", away_teams_for_select, index=0 if away_teams_for_select else None)

        if st.button("Získať predikcie pre zápas"):
            if home_team_pred and away_team_pred:
                with st.spinner(f"Získavam predikcie pre {home_team_pred} vs {away_team_pred}..."):
                    try:
                        predictor = Predictor()
                        probs = predictor.predict_probabilities(home_team_pred, away_team_pred)
                        score = predictor.predict_score(home_team_pred, away_team_pred)

                        st.subheader("Výsledky predikcie modelu:")
                        st.write(f"**Odhadované pravdepodobnosti:**")
                        st.write(f"- Domáci: {probs['prob_home_win']*100:.1f}%")
                        st.write(f"- Remíza: {probs['prob_draw']*100:.1f}%")
                        st.write(f"- Hostia: {probs['prob_away_win']*100:.1f}%")
                        st.write(f"**Odhadované skóre:** {score[0]}:{score[1]}")

                    except FileNotFoundError as e:
                        st.error(f"Chyba: {e}. Uistite sa, že modely sú natrénované.")
                    except KeyError as e:
                        st.error(f"Chyba: Tím '{e.args[0]}' nebol nájdený v tréningových dátach. Skúste iné tímy.")
                    except Exception as e:
                        st.error(f"Nastala neočakávaná chyba pri predikcii: {e}")
            else:
                st.warning("Prosím, vyberte domáci aj hosťujúci tím.")
    else:
        st.info("Zoznam tímov nie je k dispozícii. Uistite sa, že raw_results.csv existuje a obsahuje dáta.")

    # --- Hlavný obsah (Aktuálne Value Bety) ---
    st.header("Aktuálne Value Bety")

    if st.button("Načítať a analyzovať dáta"):
        with st.spinner("Načítavam kurzy a hľadám value bety..."):
            try:
                odds_data = fetch_odds()
                results = analyze_value(odds_data)
                
                if not results:
                    st.warning("Žiadne dáta o kurzoch neboli nájdené alebo analyzované.")
                    return

                df = pd.DataFrame(results)
                
                # Filter for value bets
                value_bets_df = df[
                    (df['value_home'] > value_threshold) |
                    (df['value_away'] > value_threshold) |
                    (df['value_draw'] > value_threshold)
                ]

                if not value_bets_df.empty:
                    st.success("Dáta boli úspešne načítané a analyzované!")
                    st.dataframe(value_bets_df)
                    st.info(f"Zobrazujú sa tipy s value > {value_threshold*100:.0f}%")
                else:
                    st.info(f"Žiadne value bety neboli nájdené s prahom > {value_threshold*100:.0f}%")

            except Exception as e:
                st.error(f"Nastala chyba pri načítavaní alebo analýze dát: {e}")

if __name__ == "__main__":
    main()
